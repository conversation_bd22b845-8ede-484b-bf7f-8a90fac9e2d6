# AI服务模块 - 处理DeepSeek AI调用和图片内容识别
import openai
import cv2
import numpy as np
from PIL import Image
import base64
import io
from typing import Dict, List, Any, Tuple
import config
import easyocr
import re
import cv2
import numpy as np
from PIL import Image
import pytesseract
from pytesseract import Output
from sklearn.cluster import DBSCAN
from pptx import Presentation
from pptx.util import Pt, Inches

class AIService:
    def __init__(self):
        """初始化AI服务"""
        self.client = openai.OpenAI(
            api_key=config.DEEPSEEK_API_KEY,
            base_url=config.DEEPSEEK_BASE_URL
        )
        # 初始化OCR引擎
        try:
            #self.ocr_reader = easyocr.Reader(['chinese_sim'])  # 支持中英文
            self.ocr_reader = easyocr.Reader(['ch_sim', 'en'])  # 支持中英文
        except Exception as e:
            print(f"OCR初始化失败: {e}")
            self.ocr_reader = None
    
    def analyze_image(self, image: Image.Image) -> Dict[str, Any]:
        """分析图片内容，识别PPT元素并提取可重建的信息"""
        try:
            # 将PIL图片转换为OpenCV格式
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            # 基本图片分析
            height, width = opencv_image.shape[:2]

            # OCR文字识别
            ocr_results = self._extract_text_with_ocr(image)

            # 图形元素识别
            shape_elements = self._detect_shape_elements(opencv_image)

            # 合并OCR结果和图形元素
            shape_elements = self._merge_ocr_and_shapes(ocr_results, shape_elements)

            # 布局分析
            layout_analysis = self._analyze_layout_structure(opencv_image, ocr_results, shape_elements)

            # 颜色分析
            dominant_colors = self._get_dominant_colors(opencv_image)

            # 亮度分析
            brightness = self._calculate_brightness(opencv_image)

            # AI内容理解和结构化
            structured_content = self._structure_ppt_content(ocr_results, layout_analysis)

            # 生成PPT重建信息
            ppt_reconstruction = self._generate_ppt_reconstruction_data(
                structured_content, layout_analysis, dominant_colors, shape_elements
            )

            return {
                "dimensions": {"width": width, "height": height},
                "ocr_results": ocr_results,
                "shape_elements": shape_elements,
                "layout_analysis": layout_analysis,
                "structured_content": structured_content,
                "ppt_reconstruction": ppt_reconstruction,
                "dominant_colors": dominant_colors,
                "brightness": brightness
            }
        except Exception as e:
            return {"error": f"图片分析失败: {str(e)}"}
    
    def generate_content_from_text(self, text_description: str) -> Dict[str, Any]:
        """根据文字描述生成PPT内容"""
        try:
            prompt = f"""
            基于以下描述，为单页PPT生成内容结构：
            
            描述：{text_description}
            
            请生成：
            1. 一个简洁有力的标题（不超过15个字）
            2. 3-5个要点内容（每个要点不超过20个字）
            3. 推荐的布局类型（title_content, title_image_content, full_image）
            4. 推荐的配色方案
            
            请以JSON格式返回结果。
            """
            
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的PPT设计助手，擅长根据内容生成简洁美观的PPT布局。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7
            )
            
            content = response.choices[0].message.content
            return self._parse_ai_response(content)
            
        except Exception as e:
            return {"error": f"内容生成失败: {str(e)}"}

    def analyze_image_with_ai_enhanced(self, image: Image.Image) -> Dict[str, Any]:
        """使用AI增强分析图片内容，特别关注图形元素"""
        try:
            # 基本图片分析
            basic_analysis = self.analyze_image(image)
            
            # 使用AI分析图形元素
            ai_shape_analysis = self._analyze_shapes_with_ai(image)
            
            # 合并分析结果
            enhanced_analysis = basic_analysis.copy()
            enhanced_analysis["ai_shape_analysis"] = ai_shape_analysis
            
            # 更新图形元素信息
            if "shape_elements" in enhanced_analysis and "ai_shape_analysis" in enhanced_analysis:
                enhanced_analysis["shape_elements"] = self._merge_shape_analysis(
                    enhanced_analysis["shape_elements"], 
                    enhanced_analysis["ai_shape_analysis"]
                )
            
            return enhanced_analysis
            
        except Exception as e:
            return {"error": f"AI增强分析失败: {str(e)}"}

    def _extract_text_with_ocr(self, image: Image.Image) -> List[Dict[str, Any]]:
        """使用OCR提取图片中的文字及其位置信息"""
        try:
            if self.ocr_reader is None:
                return []

            # 转换为numpy数组
            img_array = np.array(image)

            # 使用EasyOCR进行文字识别
            results = self.ocr_reader.readtext(img_array)

            ocr_data = []
            for (bbox, text, confidence) in results:
                if confidence > 0.3:  # 只保留置信度较高的结果
                    # 计算边界框信息
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]

                    ocr_data.append({
                        "text": text.strip(),
                        "bbox": {
                            "x": min(x_coords),
                            "y": min(y_coords),
                            "width": max(x_coords) - min(x_coords),
                            "height": max(y_coords) - min(y_coords)
                        },
                        "confidence": confidence,
                        "center": {
                            "x": (min(x_coords) + max(x_coords)) / 2,
                            "y": (min(y_coords) + max(y_coords)) / 2
                        }
                    })

            return ocr_data

        except Exception as e:
            print(f"OCR识别失败: {e}")
            return []

    def _merge_ocr_and_shapes(self, ocr_results: List[Dict], shape_elements: Dict) -> Dict[str, List[Dict]]:
        """合并OCR识别的文字和图形元素，优先使用OCR的bbox"""
        for ocr in ocr_results:
            bbox = ocr["bbox"]
            rect = {
                "bbox": bbox,
                "type": "rectangle",
                "from_ocr": True
            }
            shape_elements["rectangles"].append(rect)
        return shape_elements

    def _analyze_layout_structure(self, image: np.ndarray, ocr_results: List[Dict], shape_elements: Dict) -> Dict[str, Any]:
        """分析图片的布局结构"""
        try:
            height, width = image.shape[:2]

            # 根据文字位置和大小分析布局
            layout_elements = {
                "title": [],
                "subtitle": [],
                "content": [],
                "bullet_points": [],
                "rectangles": [],
                "lines": [],
                "circles": [],
                "other_shapes": []
            }

            # 按Y坐标排序文字元素
            sorted_texts = sorted(ocr_results, key=lambda x: x["center"]["y"])

            for text_info in sorted_texts:
                text = text_info["text"]
                bbox = text_info["bbox"]
                center = text_info["center"]

                # 根据位置和大小判断元素类型
                relative_y = center["y"] / height
                relative_size = (bbox["width"] * bbox["height"]) / (width * height)

                # 标题通常在上方且字体较大
                if relative_y < 0.3 and relative_size > 0.01:
                    layout_elements["title"].append(text_info)
                # 副标题
                elif relative_y < 0.4 and relative_size > 0.005:
                    layout_elements["subtitle"].append(text_info)
                # 列表项（通常以特殊字符开头）
                elif re.match(r'^[•·▪▫◦‣⁃]\s*|^\d+[.)]\s*|^[-*+]\s*', text):
                    layout_elements["bullet_points"].append(text_info)
                # 其他内容
                else:
                    layout_elements["content"].append(text_info)

            # 分析整体布局类型
            layout_type = self._determine_layout_type(layout_elements, width, height)

            # 合并图形元素到布局元素中
            for shape_type, shapes in shape_elements.items():
                if not isinstance(shapes, list):
                    continue
                for shape_info in shapes:
                    if not isinstance(shape_info, dict) or "type" not in shape_info:
                        continue
                    if shape_info["type"] == "rectangle":
                        layout_elements["rectangles"].append(shape_info)
                    elif shape_info["type"] == "line":
                        layout_elements["lines"].append(shape_info)
                    elif shape_info["type"] == "circle":
                        layout_elements["circles"].append(shape_info)
                    else:
                        layout_elements["other_shapes"].append(shape_info)

            return {
                "layout_type": layout_type,
                "elements": layout_elements,
                "dimensions": {"width": width, "height": height}
            }

        except Exception as e:
            print(f"布局分析失败: {e}")
            return {"layout_type": "unknown", "elements": {}, "dimensions": {}}

    def _determine_layout_type(self, layout_elements: Dict, width: int, height: int) -> str:
        """根据元素分布确定布局类型"""
        title_count = len(layout_elements["title"])
        content_count = len(layout_elements["content"])
        bullet_count = len(layout_elements["bullet_points"])
        rectangle_count = len(layout_elements["rectangles"])
        line_count = len(layout_elements["lines"])
        circle_count = len(layout_elements["circles"])

        if title_count > 0 and bullet_count > 2:
            return "title_with_bullets"
        elif title_count > 0 and content_count > 0:
            return "title_with_content"
        elif bullet_count > 0:
            return "bullet_list"
        elif title_count > 0:
            return "title_only"
        elif rectangle_count > 0:
            return "rectangles_only"
        elif line_count > 0:
            return "lines_only"
        elif circle_count > 0:
            return "circles_only"
        else:
            return "content_only"

    def _structure_ppt_content(self, ocr_results: List[Dict], layout_analysis: Dict) -> Dict[str, Any]:
        """将识别的内容结构化为PPT格式"""
        try:
            elements = layout_analysis.get("elements", {})

            # 提取标题
            title = ""
            if elements.get("title"):
                title = " ".join([item["text"] for item in elements["title"]])

            # 提取副标题
            subtitle = ""
            if elements.get("subtitle"):
                subtitle = " ".join([item["text"] for item in elements["subtitle"]])

            # 提取内容要点
            content_points = []

            # 处理列表项
            for bullet in elements.get("bullet_points", []):
                text = bullet["text"]
                # 清理列表标记
                clean_text = re.sub(r'^[•·▪▫◦‣⁃]\s*|^\d+[.)]\s*|^[-*+]\s*', '', text).strip()
                if clean_text:
                    content_points.append(clean_text)

            # 处理其他内容
            for content in elements.get("content", []):
                text = content["text"].strip()
                if text and len(text) > 3:  # 过滤太短的文本
                    content_points.append(text)

            # 如果没有明确的要点，尝试将长文本分割
            if not content_points and elements.get("content"):
                all_content = " ".join([item["text"] for item in elements["content"]])
                # 按句号、感叹号、问号分割
                sentences = re.split(r'[。！？.!?]\s*', all_content)
                content_points = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 5]

            return {
                "title": title or "识别的PPT内容",
                "subtitle": subtitle,
                "content_points": content_points[:8],  # 最多8个要点
                "layout_type": layout_analysis.get("layout_type", "title_with_content"),
                "total_text_elements": len(ocr_results)
            }

        except Exception as e:
            print(f"内容结构化失败: {e}")
            return {
                "title": "PPT内容识别",
                "subtitle": "",
                "content_points": ["内容识别失败"],
                "layout_type": "title_with_content",
                "total_text_elements": 0
            }

    def _generate_ppt_reconstruction_data(self, structured_content: Dict,
                                        layout_analysis: Dict,
                                        dominant_colors: List,
                                        shape_elements: Dict) -> Dict[str, Any]:
        """生成PPT重建所需的数据"""
        try:
            # 选择合适的PPT布局模板
            layout_type = structured_content.get("layout_type", "title_with_content")

            if layout_type in ["title_with_bullets", "bullet_list"]:
                ppt_layout = "title_content"
            elif layout_type == "title_only":
                ppt_layout = "title_only"
            elif layout_type == "rectangles_only":
                ppt_layout = "rectangles_only"
            elif layout_type == "lines_only":
                ppt_layout = "lines_only"
            elif layout_type == "circles_only":
                ppt_layout = "circles_only"
            else:
                ppt_layout = "title_content"

            # 生成配色方案
            color_scheme = self._generate_color_scheme(dominant_colors)

            return {
                "layout_template": ppt_layout,
                "title": structured_content.get("title", ""),
                "subtitle": structured_content.get("subtitle", ""),
                "content_points": structured_content.get("content_points", []),
                "color_scheme": color_scheme,
                "font_suggestions": {
                    "title_size": 44,
                    "content_size": 24,
                    "title_bold": True,
                    "content_bold": False
                },
                "shape_elements": shape_elements
            }

        except Exception as e:
            print(f"PPT重建数据生成失败: {e}")
            return {
                "layout_template": "title_content",
                "title": "PPT重建",
                "subtitle": "",
                "content_points": ["重建失败"],
                "color_scheme": {"background": "#FFFFFF", "text": "#000000", "accent": "#0066CC"},
                "font_suggestions": {"title_size": 44, "content_size": 24},
                "shape_elements": {"rectangles": [], "lines": [], "circles": [], "other_shapes": []}
            }

    def _generate_color_scheme(self, dominant_colors: List) -> Dict[str, str]:
        """根据主要颜色生成配色方案"""
        try:
            if not dominant_colors:
                return {"background": "#FFFFFF", "text": "#333333", "accent": "#0066CC"}

            # 转换RGB到十六进制
            def rgb_to_hex(rgb):
                return "#{:02x}{:02x}{:02x}".format(rgb[0], rgb[1], rgb[2])

            # 选择最亮的颜色作为背景
            brightest = max(dominant_colors, key=lambda c: sum(c))
            background = rgb_to_hex(brightest)

            # 选择最暗的颜色作为文字
            darkest = min(dominant_colors, key=lambda c: sum(c))
            text_color = rgb_to_hex(darkest)

            # 选择中等亮度的颜色作为强调色
            if len(dominant_colors) >= 3:
                accent_color = rgb_to_hex(dominant_colors[1])
            else:
                accent_color = "#0066CC"

            return {
                "background": background,
                "text": text_color,
                "accent": accent_color
            }

        except Exception as e:
            print(f"配色方案生成失败: {e}")
            return {"background": "#FFFFFF", "text": "#333333", "accent": "#0066CC"}
    
    def _get_dominant_colors(self, image: np.ndarray, k: int = 3) -> List[List[int]]:
        """获取图片主要颜色"""
        try:
            # 重塑图片数据
            data = image.reshape((-1, 3))
            data = np.float32(data)
            
            # 使用K-means聚类找到主要颜色
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
            _, labels, centers = cv2.kmeans(data, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
            
            # 转换为RGB格式并返回
            centers = np.uint8(centers)
            dominant_colors = []
            for center in centers:
                # BGR转RGB
                rgb_color = [int(center[2]), int(center[1]), int(center[0])]
                dominant_colors.append(rgb_color)
            
            return dominant_colors
        except:
            return [[255, 255, 255], [0, 0, 0], [128, 128, 128]]
    
    def _calculate_brightness(self, image: np.ndarray) -> float:
        """计算图片亮度"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return float(np.mean(gray)) / 255.0
        except:
            return 0.5
    
    def _image_to_base64(self, image: Image.Image) -> str:
        """将PIL图片转换为base64字符串"""
        try:
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return img_str
        except:
            return ""
    
    def _analyze_image_with_ai(self, image_base64: str) -> Dict[str, Any]:
        """使用AI分析图片内容"""
        try:
            prompt = """
            请分析这张图片并提供以下信息：
            1. 主要内容描述
            2. 图片风格（商务、学术、创意、个人等）
            3. 适合的PPT主题
            4. 建议的文字位置
            
            请以JSON格式返回结果。
            """
            
            # 注意：这里简化了实现，实际使用时需要根据DeepSeek API的具体要求调整
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的图片分析师，擅长分析图片内容并提供PPT设计建议。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            content = response.choices[0].message.content
            return self._parse_ai_response(content)
            
        except Exception as e:
            return {
                "description": "图片分析",
                "style": "通用",
                "theme": "基于图片内容",
                "text_position": "右侧"
            }
    
    def _suggest_layout_for_image(self, ai_analysis: Dict, brightness: float) -> str:
        """根据图片分析结果建议布局"""
        if brightness > 0.7:
            return "title_image_content"  # 亮图片适合文字叠加
        elif brightness < 0.3:
            return "title_content"  # 暗图片可能不适合作为背景
        else:
            return "title_image_content"  # 中等亮度适合图文并茂
    
    def _parse_ai_response(self, content: str) -> Dict[str, Any]:
        """解析AI返回的内容"""
        try:
            import json
            # 尝试解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            else:
                # 如果不是JSON格式，返回默认结构
                return {
                    "title": "AI生成标题",
                    "content": content.split('\n')[:5],
                    "layout": "title_content",
                    "colors": ["#2E3440", "#FFFFFF", "#5E81AC"]
                }
        except:
            return {
                "title": "AI生成内容",
                "content": ["要点1", "要点2", "要点3"],
                "layout": "title_content",
                "colors": ["#2E3440", "#FFFFFF", "#5E81AC"]
            }

    def _detect_shape_elements(self, image: np.ndarray) -> Dict[str, List[Dict]]:
        """检测图片中的图形元素（长方形、线条、圆形等）"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 边缘检测
            edges = cv2.Canny(blurred, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            shape_elements = {
                "rectangles": [],
                "lines": [],
                "circles": [],
                "other_shapes": []
            }
            
            height, width = image.shape[:2]
            min_area = (width * height) * 0.001  # 最小面积阈值
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < min_area:
                    continue
                
                # 获取边界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 计算轮廓的近似形状
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 分析形状类型
                shape_info = {
                    "bbox": {"x": x, "y": y, "width": w, "height": h},
                    "center": {"x": x + w//2, "y": y + h//2},
                    "area": area,
                    "contour": contour.tolist()
                }
                
                # 根据顶点数量判断形状
                num_vertices = len(approx)
                
                if num_vertices == 4:
                    # 可能是矩形
                    aspect_ratio = w / h if h > 0 else 0
                    if 0.2 < aspect_ratio < 5:  # 合理的宽高比
                        shape_info["type"] = "rectangle"
                        shape_info["aspect_ratio"] = aspect_ratio
                        shape_elements["rectangles"].append(shape_info)
                    else:
                        shape_elements["other_shapes"].append(shape_info)
                elif num_vertices == 2:
                    # 可能是线条
                    shape_info["type"] = "line"
                    shape_elements["lines"].append(shape_info)
                elif num_vertices > 8:
                    # 可能是圆形
                    # 检查轮廓的圆形度
                    circularity = 4 * np.pi * area / (cv2.arcLength(contour, True) ** 2)
                    if circularity > 0.7:
                        shape_info["type"] = "circle"
                        shape_info["circularity"] = circularity
                        shape_elements["circles"].append(shape_info)
                    else:
                        shape_elements["other_shapes"].append(shape_info)
                else:
                    shape_elements["other_shapes"].append(shape_info)
            
            # 检测水平线和垂直线
            lines = self._detect_lines(edges)
            shape_elements["lines"].extend(lines)
            
            return shape_elements
            
        except Exception as e:
            print(f"图形元素检测失败: {e}")
            return {"rectangles": [], "lines": [], "circles": [], "other_shapes": []}
    
    def _detect_lines(self, edges: np.ndarray) -> List[Dict]:
        """检测图片中的直线"""
        try:
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, 
                                   minLineLength=30, maxLineGap=10)
            
            detected_lines = []
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    
                    # 计算线条长度和角度
                    length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                    angle = np.arctan2(y2-y1, x2-x1) * 180 / np.pi
                    
                    # 判断是水平线还是垂直线
                    if abs(angle) < 10 or abs(angle - 180) < 10:
                        line_type = "horizontal"
                    elif abs(angle - 90) < 10 or abs(angle + 90) < 10:
                        line_type = "vertical"
                    else:
                        line_type = "diagonal"
                    
                    detected_lines.append({
                        "type": "line",
                        "line_type": line_type,
                        "start": {"x": x1, "y": y1},
                        "end": {"x": x2, "y": y2},
                        "length": length,
                        "angle": angle,
                        "bbox": {
                            "x": min(x1, x2),
                            "y": min(y1, y2),
                            "width": abs(x2-x1),
                            "height": abs(y2-y1)
                        },
                        "center": {
                            "x": (x1 + x2) // 2,
                            "y": (y1 + y2) // 2
                        }
                    })
            
            return detected_lines
            
        except Exception as e:
            print(f"线条检测失败: {e}")
            return []

    def _analyze_shapes_with_ai(self, image: Image.Image) -> Dict[str, Any]:
        """使用AI分析图片中的图形元素"""
        try:
            # 先用OpenCV检测图形元素，然后将结果作为文本描述给AI
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            shape_elements = self._detect_shape_elements(opencv_image)
            
            # 构建图形描述文本
            shape_description = self._build_shape_description(shape_elements, image.size)
            
            prompt = f"""
            基于以下图形检测结果，分析PPT中的图形元素布局：

            {shape_description}

            请分析这些图形元素的布局结构和视觉层次，以JSON格式返回：
            {{
                "layout_structure": "整体布局描述",
                "visual_hierarchy": "视觉层次分析",
                "design_suggestions": "设计建议"
            }}
            """
            
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的PPT设计分析专家，擅长分析图形元素布局和视觉设计。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            content = response.choices[0].message.content
            return self._parse_shape_analysis_response(content)
            
        except Exception as e:
            print(f"AI图形分析失败: {e}")
            return {}

    def _build_shape_description(self, shape_elements: Dict, image_size: tuple) -> str:
        """构建图形元素的文字描述"""
        try:
            width, height = image_size
            description = f"图片尺寸: {width}x{height}像素\n\n"
            
            # 矩形描述
            rectangles = shape_elements.get("rectangles", [])
            if rectangles:
                description += f"检测到 {len(rectangles)} 个矩形:\n"
                for i, rect in enumerate(rectangles[:5]):  # 只描述前5个
                    bbox = rect.get("bbox", {})
                    x, y = bbox.get("x", 0), bbox.get("y", 0)
                    w, h = bbox.get("width", 0), bbox.get("height", 0)
                    rel_x, rel_y = x/width, y/height
                    rel_w, rel_h = w/width, h/height
                    description += f"  矩形{i+1}: 位置({rel_x:.2f},{rel_y:.2f}), 大小({rel_w:.2f}x{rel_h:.2f})\n"
            
            # 线条描述
            lines = shape_elements.get("lines", [])
            if lines:
                description += f"检测到 {len(lines)} 条线条:\n"
                for i, line in enumerate(lines[:5]):  # 只描述前5个
                    if "start" in line and "end" in line:
                        start = line["start"]
                        end = line["end"]
                        line_type = line.get("line_type", "未知")
                        rel_start_x, rel_start_y = start["x"]/width, start["y"]/height
                        rel_end_x, rel_end_y = end["x"]/width, end["y"]/height
                        description += f"  线条{i+1}({line_type}): 从({rel_start_x:.2f},{rel_start_y:.2f})到({rel_end_x:.2f},{rel_end_y:.2f})\n"
            
            # 圆形描述
            circles = shape_elements.get("circles", [])
            if circles:
                description += f"检测到 {len(circles)} 个圆形:\n"
                for i, circle in enumerate(circles[:5]):  # 只描述前5个
                    bbox = circle.get("bbox", {})
                    x, y = bbox.get("x", 0), bbox.get("y", 0)
                    w, h = bbox.get("width", 0), bbox.get("height", 0)
                    rel_x, rel_y = x/width, y/height
                    rel_size = max(w, h)/width
                    description += f"  圆形{i+1}: 中心({rel_x:.2f},{rel_y:.2f}), 大小({rel_size:.2f})\n"
            
            # 其他形状描述
            other_shapes = shape_elements.get("other_shapes", [])
            if other_shapes:
                description += f"检测到 {len(other_shapes)} 个其他形状\n"
            
            return description
            
        except Exception as e:
            print(f"构建图形描述失败: {e}")
            return "图形检测结果分析中..."

    def _parse_shape_analysis_response(self, content: str) -> Dict[str, Any]:
        """解析AI返回的图形分析结果"""
        try:
            # 尝试提取JSON部分
            import json
            import re
            
            # 查找JSON格式的内容
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，返回空结果
                return {}
                
        except Exception as e:
            print(f"解析AI图形分析结果失败: {e}")
            return {}

    def _merge_shape_analysis(self, computer_vision_shapes: Dict, ai_shapes: Dict) -> Dict[str, Any]:
        """合并计算机视觉和AI分析的图形结果"""
        try:
            merged_shapes = computer_vision_shapes.copy()
            
            # 添加AI分析的布局信息
            if "layout_structure" in ai_shapes:
                merged_shapes["ai_layout_structure"] = ai_shapes["layout_structure"]
            if "visual_hierarchy" in ai_shapes:
                merged_shapes["ai_visual_hierarchy"] = ai_shapes["visual_hierarchy"]
            if "design_suggestions" in ai_shapes:
                merged_shapes["ai_design_suggestions"] = ai_shapes["design_suggestions"]
            
            return merged_shapes
            
        except Exception as e:
            print(f"合并图形分析结果失败: {e}")
            return computer_vision_shapes

class PPTRebuilder:
    def __init__(self, tesseract_cmd=None):
        if tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = tesseract_cmd

    def analyze_image(self, pil_image):
        ocr_boxes = self._extract_text_with_pytesseract(pil_image)
        grouped_texts = self._group_ocr_boxes(ocr_boxes)
        # 结构化分组
        height = pil_image.height
        title = []
        content = []
        footer = []
        for t in grouped_texts:
            x, y, w, h = t['bbox']
            center_y = y + h / 2
            if center_y < height * 0.2:
                title.append(t)
            elif center_y > height * 0.8:
                footer.append(t)
            else:
                content.append(t)
        opencv_shapes = self._detect_shapes(pil_image)
        elements = self._merge_text_and_shapes(content, opencv_shapes)
        elements['title'] = title
        elements['footer'] = footer
        return elements

    def _extract_text_with_pytesseract(self, pil_image):
        img = pil_image.convert("RGB")
        img_np = np.array(img)
        data = pytesseract.image_to_data(img_np, output_type=Output.DICT, lang='chi_sim+eng')
        boxes = []
        for i in range(len(data['text'])):
            if int(data['conf'][i]) > 30 and data['text'][i].strip():
                x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                boxes.append({'text': data['text'][i], 'bbox': (x, y, w, h)})
        return boxes

    def _group_ocr_boxes(self, boxes):
        # 先按y坐标聚类（行），再在每一行内按x坐标排序并合并相邻文本块
        if not boxes:
            return []
        X = np.array([[b['bbox'][0]+b['bbox'][2]/2, b['bbox'][1]+b['bbox'][3]/2] for b in boxes])
        clustering = DBSCAN(eps=40, min_samples=1).fit(X)
        for i, label in enumerate(clustering.labels_):
            boxes[i]['group'] = label
        grouped = {}
        for b in boxes:
            label = b['group']
            if label not in grouped:
                grouped[label] = []
            grouped[label].append(b)
        results = []
        for group in grouped.values():
            # 按x排序
            group = sorted(group, key=lambda x: x['bbox'][0])
            merged = []
            prev = group[0]
            for curr in group[1:]:
                # 如果两个文本块x方向距离很近，合并
                prev_x, prev_y, prev_w, prev_h = prev['bbox']
                curr_x, curr_y, curr_w, curr_h = curr['bbox']
                if curr_x - (prev_x + prev_w) < 20:  # 距离阈值可调
                    # 合并
                    new_x = prev_x
                    new_y = min(prev_y, curr_y)
                    new_w = (curr_x + curr_w) - prev_x
                    new_h = max(prev_y + prev_h, curr_y + curr_h) - new_y
                    prev = {
                        'text': prev['text'] + curr['text'],
                        'bbox': (new_x, new_y, new_w, new_h)
                    }
                else:
                    merged.append(prev)
                    prev = curr
            merged.append(prev)
            results.extend(merged)
        return results

    def _detect_shapes(self, pil_image):
        # OpenCV检测主要矩形、线条、圆形
        img = np.array(pil_image.convert("RGB"))
        gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        shapes = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 100:  # 过滤小噪声
                continue
            x, y, w, h = cv2.boundingRect(contour)
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            num_vertices = len(approx)
            if num_vertices == 4:
                shapes.append({'type': 'rectangle', 'bbox': (x, y, w, h)})
            elif num_vertices > 8:
                shapes.append({'type': 'circle', 'bbox': (x, y, w, h)})
            # 可扩展线条等
        return shapes

    def _merge_text_and_shapes(self, texts, shapes):
        # 简单策略：文本框优先用OCR聚类结果，图形只保留无文字的
        text_bboxes = [t['bbox'] for t in texts]
        def iou(b1, b2):
            x1, y1, w1, h1 = b1
            x2, y2, w2, h2 = b2
            xi1, yi1 = max(x1, x2), max(y1, y2)
            xi2, yi2 = min(x1+w1, x2+w2), min(y1+h1, y2+h2)
            inter_area = max(0, xi2-xi1) * max(0, yi2-yi1)
            union_area = w1*h1 + w2*h2 - inter_area
            return inter_area / union_area if union_area else 0
        filtered_shapes = []
        for s in shapes:
            if all(iou(s['bbox'], tb) < 0.3 for tb in text_bboxes):
                filtered_shapes.append(s)
        return {'texts': texts, 'shapes': filtered_shapes}

    def build_ppt(self, elements, pptx_path="output.pptx", slide_size=(16, 9)):
        prs = Presentation()
        prs.slide_width = Inches(slide_size[0])
        prs.slide_height = Inches(slide_size[1])
        slide = prs.slides.add_slide(prs.slide_layouts[6])  # 空白页
        # 标题
        for t in elements.get('title', []):
            x, y, w, h = t['bbox']
            left = Inches(x/100)
            top = Inches(y/100)
            width = Inches(w/100)
            height = Inches(h/100)
            txBox = slide.shapes.add_textbox(left, top, width, height)
            tf = txBox.text_frame
            tf.text = t['text']
            for p in tf.paragraphs:
                for run in p.runs:
                    run.font.size = Pt(32)
        # 内容
        for t in elements.get('texts', []):
            x, y, w, h = t['bbox']
            left = Inches(x/100)
            top = Inches(y/100)
            width = Inches(w/100)
            height = Inches(h/100)
            txBox = slide.shapes.add_textbox(left, top, width, height)
            tf = txBox.text_frame
            tf.text = t['text']
            for p in tf.paragraphs:
                for run in p.runs:
                    run.font.size = Pt(18)
        # 页脚
        for t in elements.get('footer', []):
            x, y, w, h = t['bbox']
            left = Inches(x/100)
            top = Inches(y/100)
            width = Inches(w/100)
            height = Inches(h/100)
            txBox = slide.shapes.add_textbox(left, top, width, height)
            tf = txBox.text_frame
            tf.text = t['text']
            for p in tf.paragraphs:
                for run in p.runs:
                    run.font.size = Pt(14)
        # 图形
        for s in elements['shapes']:
            x, y, w, h = s['bbox']
            left = Inches(x/100)
            top = Inches(y/100)
            width = Inches(w/100)
            height = Inches(h/100)
            if s['type'] == 'rectangle':
                slide.shapes.add_shape(
                    1,  # MSO_SHAPE.RECTANGLE
                    left, top, width, height
                )
            elif s['type'] == 'circle':
                slide.shapes.add_shape(
                    9,  # MSO_SHAPE.OVAL
                    left, top, width, height
                )
        prs.save(pptx_path)
        return pptx_path
