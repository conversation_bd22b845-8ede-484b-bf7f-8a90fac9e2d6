# PPT生成器模块
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from PIL import Image
import io
import tempfile
import os
from typing import Dict, Any, Optional
import config

class PPTGenerator:
    def __init__(self):
        """初始化PPT生成器"""
        self.presentation = None
        self.slide = None
    
    def create_presentation(self) -> Presentation:
        """创建新的PPT演示文稿"""
        self.presentation = Presentation()
        # 设置幻灯片尺寸
        self.presentation.slide_width = Inches(13.33)  # 1920px at 144 DPI
        self.presentation.slide_height = Inches(7.5)   # 1080px at 144 DPI
        return self.presentation
    
    def generate_ppt_from_text(self, content_data: Dict[str, Any]) -> Presentation:
        """根据文字内容生成PPT"""
        try:
            if not self.presentation:
                self.create_presentation()
            
            # 添加空白幻灯片
            blank_slide_layout = self.presentation.slide_layouts[6]  # 空白布局
            self.slide = self.presentation.slides.add_slide(blank_slide_layout)
            
            # 获取内容
            title = content_data.get("title", "默认标题")
            content_points = content_data.get("content", ["默认内容"])
            layout_type = content_data.get("layout", "title_content")
            colors = content_data.get("colors", config.DEFAULT_TEMPLATE)
            
            # 根据布局类型生成PPT
            if layout_type == "title_content":
                self._create_title_content_layout(title, content_points, colors)
            else:
                self._create_title_content_layout(title, content_points, colors)
            
            return self.presentation
            
        except Exception as e:
            raise Exception(f"PPT生成失败: {str(e)}")
    
    def generate_ppt_from_image_recognition(self, image_analysis: Dict[str, Any]) -> Presentation:
        """根据图片识别结果重建PPT（不插入原图片）"""
        try:
            if not self.presentation:
                self.create_presentation()

            # 添加空白幻灯片
            blank_slide_layout = self.presentation.slide_layouts[6]
            self.slide = self.presentation.slides.add_slide(blank_slide_layout)

            # 获取重建数据
            reconstruction_data = image_analysis.get("ppt_reconstruction", {})

            if not reconstruction_data:
                raise Exception("未找到PPT重建数据")

            # 提取重建信息
            title = reconstruction_data.get("title", "识别的PPT内容")
            subtitle = reconstruction_data.get("subtitle", "")
            content_points = reconstruction_data.get("content_points", [])
            layout_template = reconstruction_data.get("layout_template", "title_content")
            color_scheme = reconstruction_data.get("color_scheme", {})
            font_suggestions = reconstruction_data.get("font_suggestions", {})
            shape_elements = reconstruction_data.get("shape_elements", {})
            image_dimensions = image_analysis.get("dimensions", {})
            image_width = image_dimensions.get("width", 1920)
            image_height = image_dimensions.get("height", 1080)

            # 根据布局模板创建PPT
            if layout_template == "title_only":
                self._create_title_only_layout(title, subtitle, color_scheme, font_suggestions, shape_elements, image_width, image_height)
            else:
                self._create_reconstructed_title_content_layout(
                    title, subtitle, content_points, color_scheme, font_suggestions, shape_elements, image_width, image_height
                )

            return self.presentation

        except Exception as e:
            raise Exception(f"PPT重建失败: {str(e)}")

    def generate_ppt_from_image(self, image: Image.Image, image_analysis: Dict[str, Any],
                               title: str = "", content: list = None) -> Presentation:
        """根据图片生成PPT（保持向后兼容）"""
        # 如果有重建数据，优先使用重建功能
        if "ppt_reconstruction" in image_analysis:
            return self.generate_ppt_from_image_recognition(image_analysis)

        # 否则使用原有的图片插入方式
        try:
            if not self.presentation:
                self.create_presentation()

            # 添加空白幻灯片
            blank_slide_layout = self.presentation.slide_layouts[6]
            self.slide = self.presentation.slides.add_slide(blank_slide_layout)

            # 获取布局建议
            layout_type = image_analysis.get("layout_suggestion", "title_image_content")
            ai_analysis = image_analysis.get("ai_analysis", {})

            # 如果没有提供标题和内容，使用AI分析结果
            if not title:
                title = ai_analysis.get("theme", "基于图片的演示")
            if not content:
                content = [ai_analysis.get("description", "图片内容描述")]

            # 保存图片到临时文件
            temp_image_path = self._save_temp_image(image)

            try:
                if layout_type == "title_image_content":
                    self._create_title_image_content_layout(title, content, temp_image_path)
                elif layout_type == "full_image":
                    self._create_full_image_layout(title, temp_image_path)
                else:
                    self._create_title_image_content_layout(title, content, temp_image_path)
            finally:
                # 清理临时文件
                if os.path.exists(temp_image_path):
                    os.remove(temp_image_path)

            return self.presentation

        except Exception as e:
            raise Exception(f"基于图片的PPT生成失败: {str(e)}")
    
    def _create_title_content_layout(self, title: str, content_points: list, colors: Dict):
        """创建标题+内容布局"""
        layout = config.LAYOUT_TEMPLATES["title_content"]
        
        # 添加标题
        title_box = self.slide.shapes.add_textbox(
            Inches(layout["title"]["x"] * 13.33),
            Inches(layout["title"]["y"] * 7.5),
            Inches(layout["title"]["width"] * 13.33),
            Inches(layout["title"]["height"] * 7.5)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        title_frame.paragraphs[0].font.size = Pt(config.DEFAULT_TEMPLATE["title_font_size"])
        title_frame.paragraphs[0].font.bold = True
        title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        
        # 添加内容
        content_box = self.slide.shapes.add_textbox(
            Inches(layout["content"]["x"] * 13.33),
            Inches(layout["content"]["y"] * 7.5),
            Inches(layout["content"]["width"] * 13.33),
            Inches(layout["content"]["height"] * 7.5)
        )
        content_frame = content_box.text_frame
        
        # 添加内容要点
        for i, point in enumerate(content_points):
            if i == 0:
                p = content_frame.paragraphs[0]
            else:
                p = content_frame.add_paragraph()
            
            p.text = f"• {point}"
            p.font.size = Pt(config.DEFAULT_TEMPLATE["content_font_size"])
            p.space_after = Pt(12)
    
    def _create_title_image_content_layout(self, title: str, content_points: list, image_path: str):
        """创建标题+图片+内容布局"""
        layout = config.LAYOUT_TEMPLATES["title_image_content"]
        
        # 添加标题
        title_box = self.slide.shapes.add_textbox(
            Inches(layout["title"]["x"] * 13.33),
            Inches(layout["title"]["y"] * 7.5),
            Inches(layout["title"]["width"] * 13.33),
            Inches(layout["title"]["height"] * 7.5)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        title_frame.paragraphs[0].font.size = Pt(config.DEFAULT_TEMPLATE["title_font_size"])
        title_frame.paragraphs[0].font.bold = True
        title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        
        # 添加图片
        self.slide.shapes.add_picture(
            image_path,
            Inches(layout["image"]["x"] * 13.33),
            Inches(layout["image"]["y"] * 7.5),
            Inches(layout["image"]["width"] * 13.33),
            Inches(layout["image"]["height"] * 7.5)
        )
        
        # 添加内容
        content_box = self.slide.shapes.add_textbox(
            Inches(layout["content"]["x"] * 13.33),
            Inches(layout["content"]["y"] * 7.5),
            Inches(layout["content"]["width"] * 13.33),
            Inches(layout["content"]["height"] * 7.5)
        )
        content_frame = content_box.text_frame
        
        for i, point in enumerate(content_points):
            if i == 0:
                p = content_frame.paragraphs[0]
            else:
                p = content_frame.add_paragraph()
            
            p.text = f"• {point}"
            p.font.size = Pt(config.DEFAULT_TEMPLATE["content_font_size"])
            p.space_after = Pt(12)
    
    def _create_full_image_layout(self, title: str, image_path: str):
        """创建全图片布局"""
        layout = config.LAYOUT_TEMPLATES["full_image"]
        
        # 添加背景图片
        self.slide.shapes.add_picture(
            image_path,
            Inches(0),
            Inches(0),
            Inches(13.33),
            Inches(7.5)
        )
        
        # 添加标题覆盖层
        title_box = self.slide.shapes.add_textbox(
            Inches(layout["title_overlay"]["x"] * 13.33),
            Inches(layout["title_overlay"]["y"] * 7.5),
            Inches(layout["title_overlay"]["width"] * 13.33),
            Inches(layout["title_overlay"]["height"] * 7.5)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        title_frame.paragraphs[0].font.size = Pt(config.DEFAULT_TEMPLATE["title_font_size"])
        title_frame.paragraphs[0].font.bold = True
        title_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)  # 白色文字
        title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER

    def _create_title_only_layout(self, title: str, subtitle: str, color_scheme: Dict, font_suggestions: Dict, shape_elements: Dict = None, image_width: int = 1920, image_height: int = 1080):
        """创建仅标题布局"""
        # 设置背景颜色
        background = self.slide.background
        fill = background.fill
        fill.solid()
        bg_color = color_scheme.get("background", "#FFFFFF")
        fill.fore_color.rgb = RGBColor(*self._hex_to_rgb(bg_color))

        # 绘制图形元素（如果有）
        if shape_elements:
            self._draw_shape_elements(shape_elements, color_scheme, image_width, image_height)

        # 添加标题
        title_box = self.slide.shapes.add_textbox(
            Inches(1), Inches(2), Inches(11.33), Inches(2)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        title_frame.paragraphs[0].font.size = Pt(font_suggestions.get("title_size", 48))
        title_frame.paragraphs[0].font.bold = font_suggestions.get("title_bold", True)
        title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER

        # 设置标题颜色
        text_color = color_scheme.get("text", "#000000")
        accent_color = color_scheme.get("accent", "#0066CC")
        title_frame.paragraphs[0].font.color.rgb = RGBColor(*self._hex_to_rgb(accent_color))

        # 添加副标题（如果有）
        if subtitle:
            subtitle_box = self.slide.shapes.add_textbox(
                Inches(1), Inches(4.5), Inches(11.33), Inches(1)
            )
            subtitle_frame = subtitle_box.text_frame
            subtitle_frame.text = subtitle
            subtitle_frame.paragraphs[0].font.size = Pt(font_suggestions.get("content_size", 32))
            subtitle_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
            subtitle_frame.paragraphs[0].font.color.rgb = RGBColor(*self._hex_to_rgb(text_color))

    def _create_reconstructed_title_content_layout(self, title: str, subtitle: str,
                                                 content_points: list, color_scheme: Dict,
                                                 font_suggestions: Dict, shape_elements: Dict = None, image_width: int = 1920, image_height: int = 1080):
        """创建重建的标题+内容布局，包含图形元素"""
        # 设置背景颜色
        background = self.slide.background
        fill = background.fill
        fill.solid()
        bg_color = color_scheme.get("background", "#FFFFFF")
        fill.fore_color.rgb = RGBColor(*self._hex_to_rgb(bg_color))

        # 首先绘制图形元素（作为背景）
        if shape_elements:
            self._draw_shape_elements(shape_elements, color_scheme, image_width, image_height)

        # 添加标题
        title_box = self.slide.shapes.add_textbox(
            Inches(1), Inches(0.5), Inches(11.33), Inches(1.5)
        )
        title_frame = title_box.text_frame
        title_frame.text = title
        title_frame.paragraphs[0].font.size = Pt(font_suggestions.get("title_size", 44))
        title_frame.paragraphs[0].font.bold = font_suggestions.get("title_bold", True)
        title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER

        # 设置标题颜色
        text_color = color_scheme.get("text", "#000000")
        accent_color = color_scheme.get("accent", "#0066CC")
        title_frame.paragraphs[0].font.color.rgb = RGBColor(*self._hex_to_rgb(accent_color))

        # 添加副标题（如果有）
        content_start_y = 2.5
        if subtitle:
            subtitle_box = self.slide.shapes.add_textbox(
                Inches(1), Inches(2), Inches(11.33), Inches(0.8)
            )
            subtitle_frame = subtitle_box.text_frame
            subtitle_frame.text = subtitle
            subtitle_frame.paragraphs[0].font.size = Pt(font_suggestions.get("content_size", 28))
            subtitle_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
            subtitle_frame.paragraphs[0].font.color.rgb = RGBColor(*self._hex_to_rgb(text_color))
            content_start_y = 3.3

        # 添加内容要点
        if content_points:
            content_box = self.slide.shapes.add_textbox(
                Inches(1.5), Inches(content_start_y), Inches(10.33), Inches(4)
            )
            content_frame = content_box.text_frame

            for i, point in enumerate(content_points):
                if i == 0:
                    p = content_frame.paragraphs[0]
                else:
                    p = content_frame.add_paragraph()

                p.text = f"• {point}"
                p.font.size = Pt(font_suggestions.get("content_size", 24))
                p.font.color.rgb = RGBColor(*self._hex_to_rgb(text_color))
                p.space_after = Pt(12)

    def _draw_shape_elements(self, shape_elements: Dict, color_scheme: Dict, image_width: int, image_height: int):
        """绘制图形元素"""
        try:
            # 获取幻灯片尺寸
            slide_width = self.presentation.slide_width
            slide_height = self.presentation.slide_height
            
            # 绘制矩形
            for rect in shape_elements.get("rectangles", []):
                self._draw_rectangle(rect, color_scheme, slide_width, slide_height, image_width, image_height)
            
            # 绘制线条
            for line in shape_elements.get("lines", []):
                self._draw_line(line, color_scheme, slide_width, slide_height, image_width, image_height)
            
            # 绘制圆形
            for circle in shape_elements.get("circles", []):
                self._draw_circle(circle, color_scheme, slide_width, slide_height, image_width, image_height)
            
            # 绘制其他形状
            for shape in shape_elements.get("other_shapes", []):
                self._draw_other_shape(shape, color_scheme, slide_width, slide_height, image_width, image_height)
                
        except Exception as e:
            print(f"绘制图形元素失败: {e}")

    def _draw_rectangle(self, rect_info: Dict, color_scheme: Dict, slide_width, slide_height, image_width: int, image_height: int):
        """绘制矩形"""
        try:
            bbox = rect_info["bbox"]
            
            # 计算相对位置和尺寸
            x_ratio = bbox["x"] / image_width
            y_ratio = bbox["y"] / image_height
            width_ratio = bbox["width"] / image_width
            height_ratio = bbox["height"] / image_height
            
            # 转换为PPT中的位置和尺寸
            left = slide_width * x_ratio
            top = slide_height * y_ratio
            width = slide_width * width_ratio
            height = slide_height * height_ratio
            
            # 添加矩形
            shape = self.slide.shapes.add_shape(
                1,  # 矩形形状类型
                left, top, width, height
            )
            
            # 设置填充颜色
            fill = shape.fill
            fill.solid()
            fill.fore_color.rgb = RGBColor(*self._hex_to_rgb(color_scheme.get("accent", "#0066CC")))
            
            # 设置边框
            line = shape.line
            line.color.rgb = RGBColor(*self._hex_to_rgb(color_scheme.get("text", "#000000")))
            line.width = Pt(1)
            
        except Exception as e:
            print(f"绘制矩形失败: {e}")

    def _draw_line(self, line_info: Dict, color_scheme: Dict, slide_width, slide_height, image_width: int, image_height: int):
        """绘制线条"""
        try:
            start = line_info["start"]
            end = line_info["end"]
            
            # 计算相对位置
            x1_ratio = start["x"] / image_width
            y1_ratio = start["y"] / image_height
            x2_ratio = end["x"] / image_width
            y2_ratio = end["y"] / image_height
            
            # 转换为PPT中的位置
            x1 = slide_width * x1_ratio
            y1 = slide_height * y1_ratio
            x2 = slide_width * x2_ratio
            y2 = slide_height * y2_ratio
            
            # 添加线条
            shape = self.slide.shapes.add_connector(
                1,  # 直线连接器类型
                x1, y1, x2, y2
            )
            
            # 设置线条颜色和宽度
            line = shape.line
            line.color.rgb = RGBColor(*self._hex_to_rgb(color_scheme.get("text", "#000000")))
            line.width = Pt(2)
            
        except Exception as e:
            print(f"绘制线条失败: {e}")

    def _draw_circle(self, circle_info: Dict, color_scheme: Dict, slide_width, slide_height, image_width: int, image_height: int):
        """绘制圆形"""
        try:
            bbox = circle_info["bbox"]
            
            # 计算相对位置和尺寸
            x_ratio = bbox["x"] / image_width
            y_ratio = bbox["y"] / image_height
            size_ratio = max(bbox["width"], bbox["height"]) / max(image_width, image_height)
            
            # 转换为PPT中的位置和尺寸
            left = slide_width * x_ratio
            top = slide_height * y_ratio
            size = slide_width * size_ratio
            
            # 添加椭圆（圆形）
            shape = self.slide.shapes.add_shape(
                9,  # 椭圆形状类型
                left, top, size, size
            )
            
            # 设置填充颜色
            fill = shape.fill
            fill.solid()
            fill.fore_color.rgb = RGBColor(*self._hex_to_rgb(color_scheme.get("accent", "#0066CC")))
            
            # 设置边框
            line = shape.line
            line.color.rgb = RGBColor(*self._hex_to_rgb(color_scheme.get("text", "#000000")))
            line.width = Pt(1)
            
        except Exception as e:
            print(f"绘制圆形失败: {e}")

    def _draw_other_shape(self, shape_info: Dict, color_scheme: Dict, slide_width, slide_height, image_width: int, image_height: int):
        """绘制其他形状"""
        try:
            bbox = shape_info["bbox"]
            
            # 计算相对位置和尺寸
            x_ratio = bbox["x"] / image_width
            y_ratio = bbox["y"] / image_height
            width_ratio = bbox["width"] / image_width
            height_ratio = bbox["height"] / image_height
            
            # 转换为PPT中的位置和尺寸
            left = slide_width * x_ratio
            top = slide_height * y_ratio
            width = slide_width * width_ratio
            height = slide_height * height_ratio
            
            # 添加自由形状（多边形）
            shape = self.slide.shapes.add_shape(
                5,  # 自由形状类型
                left, top, width, height
            )
            
            # 设置填充颜色
            fill = shape.fill
            fill.solid()
            fill.fore_color.rgb = RGBColor(*self._hex_to_rgb(color_scheme.get("accent", "#0066CC")))
            
            # 设置边框
            line = shape.line
            line.color.rgb = RGBColor(*self._hex_to_rgb(color_scheme.get("text", "#000000")))
            line.width = Pt(1)
            
        except Exception as e:
            print(f"绘制其他形状失败: {e}")

    def _hex_to_rgb(self, hex_color: str) -> tuple:
        """将十六进制颜色转换为RGB元组"""
        try:
            hex_color = hex_color.lstrip('#')
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        except:
            return (0, 0, 0)  # 默认黑色
    
    def _save_temp_image(self, image: Image.Image) -> str:
        """保存图片到临时文件"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        image.save(temp_file.name, 'PNG')
        temp_file.close()
        return temp_file.name
    
    def save_presentation(self, filename: str = "generated_ppt.pptx") -> str:
        """保存PPT文件"""
        try:
            if not self.presentation:
                raise Exception("没有可保存的演示文稿")
            
            # 确保文件名以.pptx结尾
            if not filename.endswith('.pptx'):
                filename += '.pptx'
            
            self.presentation.save(filename)
            return filename
            
        except Exception as e:
            raise Exception(f"保存PPT失败: {str(e)}")
    
    def get_presentation_bytes(self) -> bytes:
        """获取PPT文件的字节数据"""
        try:
            if not self.presentation:
                raise Exception("没有可导出的演示文稿")
            
            # 保存到内存中的字节流
            ppt_bytes = io.BytesIO()
            self.presentation.save(ppt_bytes)
            ppt_bytes.seek(0)
            return ppt_bytes.getvalue()
            
        except Exception as e:
            raise Exception(f"导出PPT失败: {str(e)}")
