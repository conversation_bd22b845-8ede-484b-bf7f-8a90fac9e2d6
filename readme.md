# 🎯 AI单页PPT生成器

基于 DeepSeek AI、Langgraph、Python 和 Streamlit 的智能PPT生成工具

## 📖 项目简介

AI单页PPT生成器是一个创新的在线工具，能够根据用户提供的图片或文字描述，快速生成专业、美观的单页PPT布局。通过先进的人工智能技术，帮助用户高效创建简洁、符合需求的演示文稿，大幅提升工作效率。

## 🆕 最新功能更新

### 🔷 智能图形元素识别与重建
- **图形检测**：自动识别PPT中的长方形、线条、圆形等几何图形
- **AI增强分析**：使用DeepSeek AI分析图形元素的位置、大小、颜色和用途
- **智能重建**：根据识别的图形元素，在重建的PPT中重新绘制相应的形状
- **布局保持**：保持原PPT的图形布局和视觉层次结构

### 🎨 增强的视觉元素处理
- **矩形框识别**：检测PPT中的文本框、标题框、内容框等矩形元素
- **线条检测**：识别水平线、垂直线、对角线等分隔和装饰线条
- **圆形元素**：检测圆形图标、按钮、装饰元素等
- **颜色分析**：分析图形元素的颜色搭配和视觉风格

## 功能模块

### 1. 数据输入
- **图片上传**：用户可以通过界面上传一张图片作为 PPT 的背景或主要内容来源。系统将对图片进行分析，提取关键信息用于布局设计。
- **文字描述输入**：用户可以输入一段文字描述，系统调用AI大模型，会基于文字内容生成相应的 PPT 内容布局，包括标题、文本框、图表等元素的安排。

### 2. PPT 生成
- **智能布局设计**：系统利用 Langgraph 的语言理解和生成能力，结合 DeepSeek AI 的先进算法，以及 Python 的数据处理和图像识别技术，根据输入的图片或文字描述，自动设计出合理的单页 PPT 布局。
- **图形元素重建**：系统能够识别原PPT中的图形元素（长方形、线条、圆形等），并在重建的PPT中重新绘制这些元素，保持原有的视觉布局。
- **AI增强分析**：使用DeepSeek AI分析图片中的图形元素，提供更智能的布局识别和重建建议。

若输入图片，系统会分析图片内容，识别其中的关键元素（如人物、场景、物体等），并根据这些元素的特点和位置关系，安排 PPT 上的标题、文本框、图表等元素的位置和大小，使布局与图片内容相协调。

若输入文字描述，系统会提取文字中的关键信息，如主题、重点内容、逻辑结构等，按照一定的规则生成 PPT 的标题、正文内容、要点列表等，并合理安排它们在页面上的布局，确保信息清晰、层次分明。

- **内容填充与优化**：在生成布局的基础上，系统会根据输入的内容自动填充相应的文本、图表等元素，并对字体、字号、颜色等进行优化调整，使 PPT 整体风格统一且美观。

### 3. PPT 导出
用户在生成满意的单页 PPT 布局后，可以点击导出按钮，系统将生成的 PPT 以标准的 PPT 文件格式保存到用户的本地设备或指定的存储位置，方便用户后续使用和分享。

## 技术实现

- **Langgraph**：用于理解和处理用户输入的文字描述，提取关键信息和逻辑结构，为 PPT 内容生成和布局设计提供语言基础。
- **DeepSeek AI**：提供先进的 AI 算法支持，用于增强图片分析和文字处理能力，提升生成 PPT 的质量和智能性。
- **OpenCV**：用于图像处理和图形元素检测，识别PPT中的几何图形和视觉元素。
- **EasyOCR**：用于文字识别，提取PPT中的文字内容和位置信息。
- **Python**：作为主要的开发语言，结合相关库（如 OpenCV 用于图像处理、Pandas 用于数据处理、Matplotlib 或 Plotly 用于图表生成等）实现图片分析、文字处理、布局算法等功能。
- **Streamlit**：用于构建简洁直观的 Web 界面，方便用户上传图片、输入文字描述、查看生成的 PPT 布局以及导出 PPT 文件，实现良好的用户体验。

## 使用场景

- **商务汇报**：快速生成简洁明了的单页 PPT，用于展示关键数据、项目进展或核心观点。
- **学术演讲**：根据研究内容的图片或文字描述生成 PPT，突出重点，辅助讲解。
- **创意展示**：将创意构思通过图片或文字输入，生成具有吸引力的单页 PPT，用于展示创意亮点和思路。
- **个人分享**：制作个性化的单页 PPT，用于分享个人经历、兴趣爱好等内容。

## 项目优势

- **高效便捷**：用户无需手动设计复杂的 PPT 布局，只需提供简单的输入，即可快速生成满足需求的单页 PPT，大大节省时间和精力。
- **智能生成**：基于 Langgraph 和 DeepSeek AI 技术，能够智能分析输入内容并生成合理的布局，确保 PPT 的内容和形式相匹配。
- **图形重建**：能够识别和重建原PPT中的图形元素，保持原有的视觉布局和设计风格。
- **易于使用**：通过 Streamlit 构建的 Web 界面简洁直观，操作简单，用户无需具备专业的 PPT 制作技能即可上手使用。

## 🧪 测试功能

项目包含完整的测试脚本，可以验证图形识别功能：

```bash
# 运行基本测试
python test_shape_detection.py

# 使用真实图片测试
python test_shape_detection.py your_image.png
```

测试脚本会生成可视化结果，展示检测到的图形元素。
