#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的图形检测和PPT生成功能
"""

import sys
import os
import cv2
import numpy as np
from PIL import Image
from ai_service import AIService
from ppt_generator import PPTGenerator
# 使用Agg后端，不需要Tkinter
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建一个包含图形和文字的测试图像
def create_test_image():
    # 创建一个白色背景的图片
    img = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # 绘制矩形
    cv2.rectangle(img, (100, 100), (300, 200), (0, 0, 255), 2)
    cv2.rectangle(img, (400, 150), (600, 250), (0, 255, 0), -1)  # 填充矩形
    
    # 绘制线条
    cv2.line(img, (50, 300), (750, 300), (255, 0, 0), 3)  # 水平线
    cv2.line(img, (400, 50), (400, 550), (0, 255, 255), 2)  # 垂直线
    
    # 绘制圆形
    cv2.circle(img, (200, 450), 50, (255, 0, 255), 2)
    
    # 添加文字
    cv2.putText(img, "测试图形识别", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(img, "矩形", (150, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "线条", (350, 320), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "圆形", (180, 470), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    return Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))

def test_fixed_shape_detection():
    print("🔍 开始测试优化后的图形检测和PPT生成功能...")
    
    # 创建测试图片
    print("📸 创建测试图片...")
    test_image = create_test_image()
    test_image.save("fixed_test_image.png")
    print("✅ 测试图片已保存为 fixed_test_image.png")
    
    # 创建AI服务和PPT生成器实例
    ai_service = AIService()
    ppt_generator = PPTGenerator()
    
    # 分析图片
    print("🔍 分析测试图片...")
    image_analysis = ai_service.analyze_image(test_image)
    
    if "error" in image_analysis:
        print(f"❌ 图片分析失败: {image_analysis['error']}")
        return
    
    # 生成PPT
    print("📝 生成PPT...")
    presentation = ppt_generator.generate_ppt_from_image_recognition(image_analysis)
    
    # 保存PPT
    ppt_path = ppt_generator.save_presentation("fixed_shape_test.pptx")
    print(f"✅ PPT已保存为 {ppt_path}")
    
    # 显示原始图片和分析结果
    print("📊 显示测试结果...")
    visualize_results(test_image, image_analysis)
    
    print("🎉 测试完成！请检查生成的PPT文件。")

def visualize_results(image, image_analysis):
    """可视化原始图片和分析结果"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 显示原图
    ax1.imshow(image)
    ax1.set_title("原始图片")
    ax1.axis('off')
    
    # 显示检测结果
    ax2.imshow(image)
    ax2.set_title("检测到的图形元素")
    ax2.axis('off')
    
    # 获取检测结果
    shape_elements = image_analysis.get("shape_elements", {})
    ocr_results = image_analysis.get("ocr_results", [])
    
    # 绘制检测到的矩形
    for rect in shape_elements.get("rectangles", []):
        bbox = rect["bbox"]
        rect_patch = patches.Rectangle(
            (bbox["x"], bbox["y"]), bbox["width"], bbox["height"],
            linewidth=2, edgecolor='red', facecolor='none'
        )
        ax2.add_patch(rect_patch)
    
    # 绘制检测到的线条
    for line in shape_elements.get("lines", []):
        if "start" in line and "end" in line:
            start = line["start"]
            end = line["end"]
            ax2.plot([start["x"], end["x"]], [start["y"], end["y"]], 
                    color='blue', linewidth=3)
    
    # 绘制检测到的圆形
    for circle in shape_elements.get("circles", []):
        bbox = circle["bbox"]
        center_x = bbox["x"] + bbox["width"] // 2
        center_y = bbox["y"] + bbox["height"] // 2
        radius = min(bbox["width"], bbox["height"]) // 2
        circle_patch = patches.Circle(
            (center_x, center_y), radius,
            linewidth=2, edgecolor='green', facecolor='none'
        )
        ax2.add_patch(circle_patch)
    
    # 绘制OCR识别到的文字
    for ocr in ocr_results:
        bbox = ocr["bbox"]
        text = ocr["text"]
        ax2.text(bbox["x"], bbox["y"] - 5, text, fontsize=10, color='black', bbox=dict(facecolor='white', alpha=0.7, edgecolor='none'))
    
    plt.tight_layout()
    plt.savefig("fixed_shape_detection_results.png", dpi=300, bbox_inches='tight')
    # 保存图像但不显示
    plt.savefig("fixed_shape_detection_results.png", dpi=300, bbox_inches='tight')
    print("✅ 检测结果已保存为 fixed_shape_detection_results.png")

if __name__ == "__main__":
    test_fixed_shape_detection()